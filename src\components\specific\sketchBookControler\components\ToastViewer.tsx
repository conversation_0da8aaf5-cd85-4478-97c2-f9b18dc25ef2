import React, { useRef, useEffect } from 'react';
import { Viewer } from '@toast-ui/react-editor';
import '@toast-ui/editor/dist/toastui-editor-viewer.css';
import '@toast-ui/editor/dist/theme/toastui-editor-dark.css';
import './ToastViewer.css';
import './ToastViewerOverride.css';
import katex from 'katex';
import 'katex/dist/katex.min.css';

// Import plugins if needed
// import codeSyntaxHighlight from '@toast-ui/editor-plugin-code-syntax-highlight';
// import Prism from 'prismjs';
// import tableMergedCell from '@toast-ui/editor-plugin-table-merged-cell';

interface ToastViewerProps {
  content: string;
  theme?: 'light' | 'dark';
  style?: React.CSSProperties;
}

const ToastViewer: React.FC<ToastViewerProps> = ({
  content,
  theme = 'light',
  style,
}) => {
  const viewerRef = useRef<any>(null);

  // Update content when it changes
  useEffect(() => {
    if (viewerRef.current) {
      const instance = viewerRef.current.getInstance();
      // Ensure content is a string
      const markdownContent = typeof content === 'string' ? content : '';

      // Set the markdown content first
      instance.setMarkdown(markdownContent);

      // Process KaTeX after DOM is updated
      setTimeout(() => {
        const viewerElement = viewerRef.current?.getRootElement();
        if (viewerElement) {
          const contentElement = viewerElement.querySelector(
            '.toastui-editor-contents'
          );
          if (contentElement) {
            // Find all text nodes and process them for math expressions
            const walker = document.createTreeWalker(
              contentElement,
              NodeFilter.SHOW_TEXT,
              null
            );

            const textNodes: Text[] = [];
            let node;
            while ((node = walker.nextNode())) {
              textNodes.push(node as Text);
            }

            // First, handle display math by processing the entire content as a whole
            const fullContent = contentElement.textContent || '';
            // console.log('Full content:', fullContent);

            // Process display math $$...$$ first (multiline)
            // Use a more flexible regex that handles newlines and whitespace
            const displayMathRegex = /\$\$([\s\S]*?)\$\$/g;
            let match;
            const replacements: Array<{ original: string; rendered: string }> =
              [];

            while ((match = displayMathRegex.exec(fullContent)) !== null) {
              const fullMatch = match[0];
              let mathContent = match[1].trim(); // Remove leading/trailing whitespace

              // Clean up the math content for KaTeX
              mathContent = mathContent
                .replace(/&amp;/g, '&') // Fix HTML entities
                .replace(/\\\s*\n/g, '\\\\ ') // Fix line endings: \ followed by newline -> \\
                .replace(/\\\s*$/gm, '\\\\') // Fix line endings: \ at end of line -> \\
                .replace(/\\\\\\\s/g, '\\\\ ') // Fix triple backslashes -> double backslashes
                .trim();

              try {
                const rendered = katex.renderToString(mathContent, {
                  throwOnError: false,
                  displayMode: true,
                });

                replacements.push({
                  original: fullMatch,
                  rendered: `<div class="katex-display-block">${rendered}</div>`,
                });
              } catch (err) {
                // KaTeX display render error - skip this math expression
              }
            }

            // Apply all replacements to the DOM
            if (replacements.length > 0) {
              let currentHTML = contentElement.innerHTML;

              replacements.forEach(({ original, rendered }) => {
                // Convert the original text to match the HTML structure
                // Replace newlines with <br> and escape HTML entities
                let htmlPattern = original
                  .replace(/&/g, '&amp;')
                  .replace(/\n/g, '<br>\n')
                  .replace(/\$/g, '\\$'); // Escape $ for regex

                // Try to find and replace the pattern in HTML
                const regex = new RegExp(
                  htmlPattern.replace(/[.*+?^{}()|[\]\\]/g, '\\$&'),
                  'g'
                );
                const newHTML = currentHTML.replace(regex, rendered);

                if (newHTML !== currentHTML) {
                  currentHTML = newHTML;
                } else {
                  // Alternative approach: find the paragraph containing $$ and replace it
                  const paragraphRegex = /<p[^>]*>\$\$<br>[\s\S]*?\$\$<\/p>/g;
                  const altReplacement = currentHTML.replace(
                    paragraphRegex,
                    rendered
                  );

                  if (altReplacement !== currentHTML) {
                    currentHTML = altReplacement;
                  }
                }
              });

              contentElement.innerHTML = currentHTML;
            }

            // Then process inline math $...$
            setTimeout(() => {
              const walker = document.createTreeWalker(
                contentElement,
                NodeFilter.SHOW_TEXT,
                null
              );

              const textNodes: Text[] = [];
              let node;
              while ((node = walker.nextNode())) {
                textNodes.push(node as Text);
              }

              textNodes.forEach((textNode) => {
                const content = textNode.textContent || '';
                if (content.includes('$') && !content.includes('$$')) {
                  // Only process single $ (inline math)
                  let hasChanges = false;
                  let processed = content;

                  // Process inline math $...$
                  processed = processed.replace(
                    /\$([^$\n]+)\$/g,
                    (match, mathContent) => {
                      try {
                        hasChanges = true;
                        return katex.renderToString(mathContent, {
                          throwOnError: false,
                          displayMode: false,
                        });
                      } catch (err) {
                        return match;
                      }
                    }
                  );

                  if (hasChanges) {
                    // Create a temporary element to hold the processed HTML
                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = processed;

                    // Replace the text node with the processed content
                    const fragment = document.createDocumentFragment();
                    while (tempDiv.firstChild) {
                      fragment.appendChild(tempDiv.firstChild);
                    }

                    if (textNode.parentNode) {
                      textNode.parentNode.replaceChild(fragment, textNode);
                    }
                  }
                }
              });
            }, 50); // Small delay to ensure display math is processed first
          }
        }
      }, 150); // Increased delay to ensure DOM is fully rendered
    }
  }, [content]);

  return (
    <div
      className={`toast-viewer-wrapper ${theme === 'dark' ? 'dark-theme' : ''}`}
      style={style}
    >
      <Viewer
        ref={viewerRef}
        initialValue={content}
        theme={theme}
        extendedAutolinks={true}
        linkAttributes={{ target: '_blank' }}
        // Add plugins if needed
        // plugins={[
        //   [codeSyntaxHighlight, { highlighter: Prism }],
        //   tableMergedCell
        // ]}
      />
    </div>
  );
};

export default ToastViewer;
