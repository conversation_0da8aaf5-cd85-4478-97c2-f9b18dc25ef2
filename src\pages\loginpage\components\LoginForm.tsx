import React from 'react';
import { Controller } from 'react-hook-form';
import { useNavigate, Link } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import toast from 'react-hot-toast';
import CustomButton from '../../../components/common/button/CustomButton';
import CustomInput from '../../../components/common/input/CustomInput';
import { useLoginForm } from '../../../hooks/useAuthForm';
import { useSigninMutation } from '../../../services/authService';
import { SigninCredential } from '../../../types/authtypes';
import useLocalStorage from '../../../hooks/useLocalStorage';
import { setAuth } from '../../../store/authSlice';
import { BASE_URL } from '../../../services/config';
import styles from '../LoginPage.module.css';

const LoginForm: React.FC = () => {
  const {
    control,
    handleSubmit,
    formState: { errors },
    trigger,
  } = useLoginForm();

  const navigate = useNavigate();
  const [signin, { isLoading }] = useSigninMutation();
  const dispatch = useDispatch();
  const [, setToken] = useLocalStorage('token', null);
  const [, setUser] = useLocalStorage('user', null);

  const handleLogin = async (data: SigninCredential) => {
    const isValid = await trigger();
    if (!isValid) return;

    try {
      const responseData = await signin(data).unwrap();

      if (responseData.data.user.isEnabled === false) {
        toast.error('Your account is disabled');
        return;
      }
      const newUserData = {
        ...responseData.data.user,
        profileImgThumbnail: responseData.data.user.profileImg
          ? `${BASE_URL}${responseData.data.user.profileImgThumbnail}`
          : null,
        profileImg: responseData.data.user.profileImg
          ? `${BASE_URL}${responseData.data.user.profileImg}`
          : null,
      };

      setToken(responseData.data.token);
      setUser(newUserData);
      dispatch(
        setAuth({
          token: responseData.data.token,
          userDetails: newUserData,
        })
      );

      toast.success('Login Successful');
      navigate('/');
    } catch (err: any) {
      const errorMessage =
        err.data?.message === 'Bad credentials'
          ? 'Invalid credentials'
          : err.data?.message || err.message || 'Login failed';
      toast.error(errorMessage);
      console.error('Login failed:', err);
    }
  };

  return (
    <>
      <form className={styles.formContainer}>
        <Controller
          name="email"
          control={control}
          render={({ field }) => (
            <CustomInput
              label="User Id"
              type="text"
              placeholder="Enter Your User Id"
              {...field}
              helperText={errors.email?.message}
              error={!!errors.email?.message}
              disableDarkTheme={true}
            />
          )}
        />
        <Controller
          name="password"
          control={control}
          render={({ field }) => (
            <CustomInput
              label="Password"
              type="password"
              placeholder="Enter Your Password"
              {...field}
              helperText={errors.password?.message}
              error={!!errors.password?.message}
              disableDarkTheme={true}
            />
          )}
        />
        <CustomButton
          label={isLoading ? 'Logging you in...' : 'Login'}
          type="primary"
          onClick={handleSubmit(handleLogin)}
          disabled={isLoading}
        />
        <div className={styles.linkContainer}>
          Don't have an account yet?{' '}
          <Link to="/signup" className={styles.link}>
            signup
          </Link>
        </div>
      </form>
    </>
  );
};

export default LoginForm;
