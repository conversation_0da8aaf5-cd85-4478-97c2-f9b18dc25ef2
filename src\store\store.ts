import { configureStore } from '@reduxjs/toolkit';
import authReducer from './authSlice';
import profile from './imageSlice';
import { authApi } from '../services/authService';
import { userMgtService } from '../services/userMgtService';
import { projectService } from '../services/projectsService';
import { sketchboookServices } from '../services/sketchbookServices';
import { chatApi } from '../services/chatServices';
import { workflowServices } from '../services/workflowServices';
import { paymentApi } from '../services/paymentService';
import { notificationApi } from '../services/notificationService';
import projectReducer from './slices/projectSlice';
import sketchbookReducer from './sketchbookSlice';
import settingsReducer from './settingsSlice';
import paymentReducer from './slices/paymentSlice';
import notificationReducer from './slices/notificationSlice';
import { complianceService } from '../services/complianceServices';

export const store: any = configureStore({
  reducer: {
    [authApi.reducerPath]: authApi.reducer,
    [chatApi.reducerPath]: chatApi.reducer,
    [userMgtService.reducerPath]: userMgtService.reducer,
    [projectService.reducerPath]: projectService.reducer,
    [sketchboookServices.reducerPath]: sketchboookServices.reducer,
    [workflowServices.reducerPath]: workflowServices.reducer,
    [complianceService.reducerPath]: complianceService.reducer,
    [paymentApi.reducerPath]: paymentApi.reducer,
    [notificationApi.reducerPath]: notificationApi.reducer,
    auth: authReducer,
    profile: profile,
    sketchbook: sketchbookReducer,
    settings: settingsReducer,
    project: projectReducer,
    payment: paymentReducer,
    notifications: notificationReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // Ignore these action types
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
        // Ignore these field paths in all actions
        ignoredActionsPaths: ['meta.arg', 'payload.timestamp'],
        // Ignore these paths in the state
        ignoredPaths: ['items.dates'],
        // Increase the warning threshold to 100ms
        warnAfter: 100,
      },
    })
      .concat(authApi.middleware)
      .concat(chatApi.middleware)
      .concat(userMgtService.middleware)
      .concat(projectService.middleware)
      .concat(sketchboookServices.middleware)
      .concat(workflowServices.middleware)
      .concat(complianceService.middleware)
      .concat(paymentApi.middleware)
      .concat(notificationApi.middleware),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
